#!/usr/bin/env python3
"""
Battery Timeline Analyzer

This script loads battery lifecycle timelines and battery type data,
cleans the data, and outputs an analysis file called accure.csv.

Data Sources:
- battery_lifecycle_timelines.csv (battery timeline data)
- battery_type.csv (battery type information)
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import sys
from typing import Dict, List, Optional, Set
import warnings

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("analyze_battery_timeline.log"),
    ],
)
logger = logging.getLogger(__name__)


class BatteryTimelineAnalyzer:
    """Analyze battery timeline data and generate accure.csv output."""

    def __init__(self):
        self.today = datetime.now().date()

        # Data containers
        self.timeline_df = None
        self.battery_type_df = None

        # Processing results
        self.battery_timelines = {}  # Pre-index by battery_id for fast lookup
        self.accure_data = []

        self.stats = {
            "total_batteries": 0,
            "batteries_with_type": 0,
            "batteries_without_type": 0,
            "errors": [],
        }

    def load_data(self):
        """Load all required data files."""
        logger.info("Loading data files...")

        # Load battery lifecycle timelines
        logger.info("Loading battery lifecycle timelines...")
        try:
            self.timeline_df = pd.read_csv("battery_lifecycle_timelines.csv")
            logger.info(f"Loaded {len(self.timeline_df)} timeline records")
        except FileNotFoundError:
            logger.error("battery_lifecycle_timelines.csv not found")
            raise
        except Exception as e:
            logger.error(f"Error loading battery_lifecycle_timelines.csv: {e}")
            raise

        # Load battery type data
        logger.info("Loading battery type data...")
        try:
            self.battery_type_df = pd.read_csv("battery_type.csv")
            logger.info(f"Loaded {len(self.battery_type_df)} battery type records")
        except FileNotFoundError:
            logger.error("battery_type.csv not found")
            raise
        except Exception as e:
            logger.error(f"Error loading battery_type.csv: {e}")
            raise

    def clean_data(self):
        """Clean and prepare data for processing."""
        logger.info("Cleaning data...")

        # Clean timeline data
        if self.timeline_df is not None:
            # Convert battery_id to string for consistent handling
            self.timeline_df["battery_id"] = self.timeline_df["battery_id"].astype(str)

            initial_count = len(self.timeline_df)
            self.timeline_df = self.timeline_df.dropna(subset=["battery_id"])

            self.timeline_df = self.timeline_df[
                ~self.timeline_df["battery_id"].str.contains(
                    "kein Tausch", case=False, na=False
                )
            ]

            # Remove records where interval_start >= interval_end (invalid/zero-duration intervals)
            before_interval_check = len(self.timeline_df)

            # Convert to datetime for comparison
            start_dates = pd.to_datetime(
                self.timeline_df["interval_start"], errors="coerce"
            )
            end_dates = pd.to_datetime(
                self.timeline_df["interval_end"], errors="coerce"
            )

            valid_intervals = (
                (start_dates < end_dates) & start_dates.notna() & end_dates.notna()
            )

            self.timeline_df = self.timeline_df[valid_intervals]

            after_interval_check = len(self.timeline_df)
            if before_interval_check != after_interval_check:
                logger.info(
                    f"Removed {before_interval_check - after_interval_check} rows with invalid/zero-duration intervals"
                )

            final_count = len(self.timeline_df)

            if initial_count != final_count:
                logger.info(
                    f"Total removed: {initial_count - final_count} rows (missing battery_id + invalid intervals)"
                )

        # Clean battery type data
        if self.battery_type_df is not None:
            self.battery_type_df["battery_id"] = self.battery_type_df[
                "battery_id"
            ].astype(str)

            self.battery_type_df = self.battery_type_df[
                ~self.battery_type_df["battery_id"].str.contains(
                    "kein Tausch", case=False, na=False
                )
            ]

            self.battery_type_df = self.battery_type_df[
                ["battery_id", "battery_type"]
            ].copy()

            # Remove duplicates
            initial_count = len(self.battery_type_df)
            self.battery_type_df = self.battery_type_df.drop_duplicates(
                subset=["battery_id"], keep="first"
            )
            final_count = len(self.battery_type_df)

            if initial_count != final_count:
                logger.info(
                    f"Removed {initial_count - final_count} duplicate battery_id entries"
                )

        logger.info(
            f"After cleaning: {len(self.timeline_df)} timeline records, {len(self.battery_type_df)} battery type records"
        )

    def _calculate_battery_age(self, battery_id: str) -> dict:
        """
        Calculate battery age based on timeline intervals.

        Args:
            battery_id: The battery ID to calculate age for

        Returns:
            dict: Contains battery_age, confidence, is_active, vin, note, lifecycle_start, lifecycle_end
        """
        # Default return values
        result = {
            "battery_age": "",
            "confidence": "",
            "is_active": False,
            "source_event_ids": [],
            "vin": "",
            "note": "",
            "lifecycle_start": "",
            "lifecycle_end": "",
        }

        # Get battery timeline
        if battery_id not in self.battery_timelines:
            result["note"] = "No timeline data found"
            return result

        timeline = self.battery_timelines[battery_id]

        # Sort chronologically by interval_start
        timeline = timeline.sort_values("interval_start", na_position="last")

        # Convert interval_start and interval_end to datetime for processing
        timeline = timeline.copy()
        timeline["interval_start"] = pd.to_datetime(
            timeline["interval_start"], errors="coerce"
        )
        timeline["interval_end"] = pd.to_datetime(
            timeline["interval_end"], errors="coerce"
        )

        initial_interval = timeline.iloc[0] if len(timeline) > 0 else None
        last_interval = timeline.iloc[-1] if len(timeline) > 0 else None

        # Determine if battery is active (end date equals today)
        is_active = last_interval is not None and last_interval["interval_end"] == pd.Timestamp(self.today)
        result["is_active"] = is_active
        result["vin"] = last_interval["vin"] if pd.notna(last_interval["vin"]) else ""

        # Set lifecycle start and end dates
        if initial_interval is not None and pd.notna(initial_interval["interval_start"]):
            result["lifecycle_start"] = initial_interval["interval_start"].date()
        
        if last_interval is not None and pd.notna(last_interval["interval_end"]):
            result["lifecycle_end"] = last_interval["interval_end"].date()

        # Calculate age using start and end dates (both should be available now)
        start_date = initial_interval["interval_start"]
        end_date = last_interval["interval_end"]

        if pd.notna(start_date) and pd.notna(end_date):
            age_days = (end_date - start_date).days
            result["battery_age"] = age_days

            # Calculate confidence (average of first and last interval confidence)
            first_confidence = (
                initial_interval["confidence"]
                if pd.notna(initial_interval["confidence"])
                else 0.0
            )
            last_confidence = (
                last_interval["confidence"]
                if pd.notna(last_interval["confidence"])
                else 0.0
            )

            result["confidence"] = round((first_confidence + last_confidence) / 2, 2)
        else:
            result["note"] = "Missing start or end date"

        result["source_event_ids"] = last_interval[
            "source_event_ids"
        ]  # used for conflict resolution
        return result

    def process_data(self):
        """Process the data to create accure.csv output."""
        logger.info("Processing data for accure.csv output...")

        if self.battery_type_df is None or self.timeline_df is None:
            logger.warning("No data to process")
            return

        self.battery_type_df = self.battery_type_df.sort_values(
            "battery_id"
        ).reset_index(drop=True)
        self.timeline_df = self.timeline_df.sort_values("battery_id").reset_index(
            drop=True
        )

        logger.info("Pre-indexing battery timelines for fast lookup...")
        for battery_id, group in self.timeline_df.groupby("battery_id"):
            # Sort by date for each vehicle
            battery_timeline = group.sort_values("interval_start").copy()
            self.battery_timelines[battery_id] = battery_timeline

        # Get unique batteries from timeline data
        unique_batteries = self.battery_timelines.keys()
        self.stats["total_batteries"] = len(unique_batteries)

        logger.info(f"Processing {len(unique_batteries)} unique batteries")

        # Create battery type lookup dictionary
        battery_type_lookup = {}
        if self.battery_type_df is not None:
            battery_type_lookup = dict(
                zip(
                    self.battery_type_df["battery_id"],
                    self.battery_type_df["battery_type"],
                )
            )

        for battery_id in unique_batteries:
            battery_type = battery_type_lookup.get(battery_id, "")

            if battery_type:
                self.stats["batteries_with_type"] += 1
            else:
                self.stats["batteries_without_type"] += 1

            # Calculate battery age and get additional info
            age_info = self._calculate_battery_age(battery_id)

            # Create record for accure.csv
            accure_record = {
                "battery_id": battery_id,
                "battery_type": battery_type,
                "vin": age_info["vin"],
                "battery_age": age_info["battery_age"],
                "confidence": age_info["confidence"],
                "is_active": age_info["is_active"],
                "source_event_ids": age_info["source_event_ids"],
                "note": age_info["note"],
                "lifecycle_start": age_info["lifecycle_start"],
                "lifecycle_end": age_info["lifecycle_end"],
            }

            self.accure_data.append(accure_record)

        logger.info(f"Processed {len(self.accure_data)} battery records")
        logger.info(f"Batteries with type: {self.stats['batteries_with_type']}")
        logger.info(f"Batteries without type: {self.stats['batteries_without_type']}")

    def generate_output(self):
        """Generate the accure.csv output file."""
        logger.info("Generating accure.csv output...")

        if not self.accure_data:
            logger.warning("No data to output")
            return

        accure_df = pd.DataFrame(self.accure_data)

        accure_df = accure_df.sort_values("battery_id")

        output_filename = "accure.csv"
        accure_df.to_csv(output_filename, index=False)

        logger.info(f"Saved {len(accure_df)} records to {output_filename}")

        # Generate statistics
        stats_filename = "analyze_battery_timeline_statistics.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Timeline Analysis Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {self.stats['total_batteries']}\n")
            f.write(f"Batteries with Type: {self.stats['batteries_with_type']}\n")
            f.write(f"Batteries without Type: {self.stats['batteries_without_type']}\n")
            f.write(f"Output File: {output_filename}\n")

            if self.stats["errors"]:
                f.write("\nErrors:\n")
                for error in self.stats["errors"]:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        return output_filename, stats_filename

    def validate_and_resolve_conflicts(self):
        """Validate that each VIN has at most 2 active batteries (master/slave configuration)."""
        logger.info("Validating result...")
        if not self.accure_data:
            logger.warning("No data to validate")
            return

        # Group by VIN using native Python
        vin_groups = {}
        for i, record in enumerate(self.accure_data):
            vin = record["vin"]
            if vin not in vin_groups:
                vin_groups[vin] = []
            vin_groups[vin].append((i, record))

        for vin, records in vin_groups.items():
            active_records = [
                (i, record) for i, record in records if record["is_active"]
            ]

            if len(active_records) > 2:  # Allow 2 concurrent batteries (master/slave)
                logger.warning(f"VIN {vin} has {len(active_records)} active batteries (expected ≤2)")
                # Log the VIN and the active batteries for debugging, each on a new line
                logger.warning(f"  → Active batteries for VIN {vin}:")
                for i, record in active_records:
                    logger.warning(
                        f"    → Battery {record['battery_id']} with source_event_ids {record['source_event_ids']} and confidence {record['confidence']}"
                    )
                self.stats["errors"].append(f"VIN {vin} has multiple active batteries")

                # Sort by source_event_ids descending and keep top 2
                active_records_sorted = sorted(
                    active_records, 
                    key=lambda x: x[1]["source_event_ids"], 
                    reverse=True
                )
                
                # Keep top 2, deactivate the rest
                batteries_to_keep = set()
                for i in range(min(2, len(active_records_sorted))):
                    batteries_to_keep.add(active_records_sorted[i][1]["source_event_ids"])
                
                # Deactivate all except the top 2
                for i, record in active_records:
                    if record["source_event_ids"] not in batteries_to_keep:
                        self.accure_data[i]["is_active"] = False
                        self.accure_data[i]["note"] = "Deactivated due to conflict resolution - kept top 2 source_event_ids"

                kept_ids = [str(id) for id in sorted(batteries_to_keep, reverse=True)]
                logger.info(
                    f"  → Resolved: Kept batteries with source_event_ids {kept_ids} active for VIN {vin}"
                )

    def run(self):
        """Run the complete analysis pipeline."""
        logger.info("Starting battery timeline analysis...")

        try:
            self.load_data()
            self.clean_data()
            self.process_data()
            self.validate_and_resolve_conflicts()
            output_file, stats_file = self.generate_output()

            logger.info("Analysis completed successfully!")
            logger.info(f"Output files: {output_file}, {stats_file}")

            return output_file, stats_file

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            self.stats["errors"].append(str(e))
            raise


def main():
    """Main entry point."""
    analyzer = BatteryTimelineAnalyzer()

    try:
        output_file, stats_file = analyzer.run()
        print(f"\n✅ Analysis completed successfully!")
        print(f"📄 Output file: {output_file}")
        print(f"📊 Statistics file: {stats_file}")

    except Exception as e:
        print(f"\n❌ Analysis failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
